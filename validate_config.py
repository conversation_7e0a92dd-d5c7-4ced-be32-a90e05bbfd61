#!/usr/bin/env python3
"""
配置验证脚本
检查应用配置是否正确设置
"""
import os
import sys
from config.app_config import load_config, check_required_env_vars, get_mysql_config, get_redis_config

def validate_config():
    """验证应用配置"""
    print("=== 网维小智配置验证 ===\n")
    
    # 加载配置
    print("1. 加载配置...")
    try:
        load_config()
        print("✓ 配置加载成功\n")
    except Exception as e:
        print(f"✗ 配置加载失败: {e}\n")
        return False
    
    # 检查必要的环境变量
    print("2. 检查必要的环境变量...")
    is_valid, error_msg = check_required_env_vars()
    if is_valid:
        print("✓ 所有必要的环境变量都已设置\n")
    else:
        print(f"✗ {error_msg}\n")
        return False
    
    # 验证MySQL配置
    print("3. 验证MySQL配置...")
    try:
        mysql_config = get_mysql_config()
        print("✓ MySQL配置获取成功")
        print(f"  - 主机: {mysql_config['host']}")
        print(f"  - 端口: {mysql_config['port']}")
        print(f"  - 用户: {mysql_config['user']}")
        print(f"  - 数据库: {mysql_config['db']}")
        print(f"  - 连接池大小: {mysql_config['pool_size']}")
        print()
    except Exception as e:
        print(f"✗ MySQL配置验证失败: {e}\n")
        return False
    
    # 验证Redis配置
    print("4. 验证Redis配置...")
    try:
        redis_config = get_redis_config()
        print("✓ Redis配置获取成功")
        if 'sentinel_nodes' in redis_config:
            print(f"  - 模式: Sentinel")
            print(f"  - Master: {redis_config['sentinel_master']}")
            print(f"  - 节点数: {len(redis_config['sentinel_nodes'])}")
        else:
            print(f"  - 模式: 单机")
            print(f"  - 主机: {redis_config['host']}")
            print(f"  - 端口: {redis_config['port']}")
        print(f"  - 数据库: {redis_config['db']}")
        print()
    except Exception as e:
        print(f"✗ Redis配置验证失败: {e}\n")
        return False
    
    # 检查API密钥
    print("5. 检查API密钥...")
    api_keys = [
        ("DASHSCOPE_API_KEY", "阿里云百炼"),
        ("OPENAI_API_KEY", "OpenAI"),
        ("DEEPSEEK_API_KEY", "DeepSeek"),
    ]
    
    for key, name in api_keys:
        value = os.getenv(key)
        if value:
            print(f"✓ {name} API密钥已设置")
        else:
            print(f"⚠ {name} API密钥未设置")
    print()
    
    # 检查模型配置
    print("6. 检查模型配置...")
    models = [
        ("AGENT_MODEL_NAME", "主要对话模型"),
        ("INTENT_MODEL_NAME", "意图识别模型"),
        ("TITLE_MODEL_NAME", "标题生成模型"),
    ]
    
    for key, name in models:
        value = os.getenv(key)
        if value:
            print(f"✓ {name}: {value}")
        else:
            print(f"⚠ {name}未设置")
    print()
    
    # 检查服务器配置
    print("7. 检查服务器配置...")
    host = os.getenv("HOST", "0.0.0.0")
    port = os.getenv("PORT", "8000")
    print(f"✓ 服务器地址: {host}:{port}")
    print()
    
    print("=== 配置验证完成 ===")
    print("✓ 所有配置验证通过，应用可以正常启动！")
    return True

def show_config_files():
    """显示配置文件状态"""
    print("\n=== 配置文件状态 ===")
    
    files_to_check = [
        ("app.properties", "Properties配置文件"),
        ("app.properties.example", "Properties模板文件"),
        (".env", "环境变量文件（向后兼容）"),
        (".env_example", "环境变量模板文件"),
    ]
    
    for filename, description in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✓ {description}: {filename} ({size} bytes)")
        else:
            print(f"✗ {description}: {filename} (不存在)")

if __name__ == "__main__":
    try:
        show_config_files()
        success = validate_config()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n验证过程中发生错误: {e}")
        sys.exit(1)

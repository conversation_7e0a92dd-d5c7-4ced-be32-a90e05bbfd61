# 网维小智 - 网吧维护助手

网维小智，一个基于 LangGraph 和 FastAPI 构建的网吧维护助手系统。

## 特性

- 游戏资源管理：下载、更新、查询等操作
- 硬件配置信息查询和分析
- 用户和网吧信息管理
- 提供流式聊天接口

## 部署准备

### 环境要求

- Docker
- 外部 MySQL 数据库

### 配置应用参数

项目支持两种配置方式：

#### 方式1：使用Properties文件（推荐）

在项目根目录创建 `app.properties` 文件：

```bash
# 复制模板文件
cp app.properties.example app.properties
```

然后编辑 `app.properties` 文件，设置以下关键配置：

```properties
# 数据库配置
MYSQL_HOST=您的数据库主机地址
MYSQL_PORT=3306
MYSQL_USER=您的数据库用户名
MYSQL_PASSWORD=您的数据库密码
MYSQL_DB=您的数据库名称
MYSQL_POOL_SIZE=5
MYSQL_MAX_POOL_SIZE=10

# API密钥配置
DASHSCOPE_API_KEY=sk-your-api-key
DEEPSEEK_API_KEY=sk-your-deepseek-key

# 服务器配置
HOST=0.0.0.0
PORT=8000
```

#### 方式2：使用环境变量（向后兼容）

如果不存在 `app.properties` 文件，系统会自动使用环境变量。可以创建 `.env` 文件或直接设置系统环境变量。

## 使用 Docker 部署

### 构建并运行容器

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 查看日志

```bash
docker-compose logs -f
```

### 停止服务

```bash
docker-compose down
```

## API 文档

部署后可以通过访问以下地址查看 API 文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要 API 端点

- `/chat_stream`: 聊天流式接口
- `/chat_history`: 获取聊天历史记录 
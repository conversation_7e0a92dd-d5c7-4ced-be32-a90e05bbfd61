"""
应用配置模块
从properties文件或环境变量加载和管理应用的配置参数
"""
import os
import sys
from dotenv import load_dotenv
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
from utils.properties_loader import load_properties_to_env

def load_config():
    """
    加载应用配置
    优先从properties文件加载，如果不存在则使用环境变量
    """
    # 检查是否指定了properties文件路径
    properties_path = os.getenv("PROPERTIES_PATH", "app.properties")

    # 如果properties文件存在，则加载它
    if os.path.exists(properties_path):
        try:
            load_properties_to_env(properties_path)
            print(f"已从properties文件加载配置: {properties_path}")
        except Exception as e:
            print(f"警告: 加载properties文件失败，将使用环境变量: {e}")
    else:
        print(f"Properties文件不存在: {properties_path}，将使用环境变量")
        DOTENV_PATH = os.environ.get("DOTENV_PATH", ".env")
        load_dotenv(DOTENV_PATH)

# 从环境变量读取日志目录，默认为当前目录下的logs
log_dir = os.getenv("LOG_DIR", os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs"))

# Druid默认的RSA密钥对
DEFAULT_PRIVATE_KEY_STRING = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAocbCrurZGbC5GArEHKlAfDSZi7gFBnd4yxOt0rwTqKBFzGyhtQLu5PRKjEiOXVa95aeIIBJ6OhC2f8FjqFUpawIDAQABAkAPejKaBYHrwUqUEEOe8lpnB6lBAsQIUFnQI/vXU4MV+MhIzW0BLVZCiarIQqUXeOhThVWXKFt8GxCykrrUsQ6BAiEA4vMVxEHBovz1di3aozzFvSMdsjTcYRRo82hS5Ru2/OECIQC2fAPoXixVTVY7bNMeuxCP4954ZkXp7fEPDINCjcQDywIgcc8XLkkPcs3Jxk7uYofaXaPbg39wuJpEmzPIxi3k0OECIGubmdpOnin3HuCP/bbjbJLNNoUdGiEmFL5hDI4UdwAdAiEAtcAwbm08bKN7pwwvyqaCBC//VnEWaq39DCzxr+Z2EIk="
DEFAULT_PUBLIC_KEY_STRING = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ=="

def decrypt_druid_password(encrypted_password, public_key_string=None):
    """
    解密Druid加密的密码
    注意：Druid使用私钥加密，公钥解密的方式

    Args:
        encrypted_password (str): 加密后的密码（Base64编码）
        public_key_string (str, optional): 公钥字符串，如果不提供则使用默认公钥

    Returns:
        str: 解密后的明文密码

    Raises:
        Exception: 解密失败时抛出异常
    """
    try:
        # 使用提供的公钥或默认公钥
        if public_key_string is None:
            public_key_string = DEFAULT_PUBLIC_KEY_STRING

        # Base64解码公钥
        public_key_bytes = base64.b64decode(public_key_string)

        # 加载公钥
        public_key = serialization.load_der_public_key(
            public_key_bytes,
            backend=default_backend()
        )

        # Base64解码加密的密码
        encrypted_bytes = base64.b64decode(encrypted_password)

        # 获取公钥的数字参数
        public_numbers = public_key.public_numbers()
        n = public_numbers.n
        e = public_numbers.e

        # 将加密数据转换为整数
        encrypted_int = int.from_bytes(encrypted_bytes, byteorder='big')

        # 使用公钥进行"解密"（实际上是RSA的验证操作）
        decrypted_int = pow(encrypted_int, e, n)

        # 转换回字节
        # 确保使用完整的密钥长度
        key_size = (n.bit_length() + 7) // 8
        decrypted_bytes = decrypted_int.to_bytes(key_size, byteorder='big')

        # 移除PKCS1填充
        if len(decrypted_bytes) < 2 or decrypted_bytes[0] != 0x00 or decrypted_bytes[1] != 0x01:
            raise Exception("无效的PKCS1填充")

        # 找到填充结束位置（0x00字节）
        separator_index = -1
        for i in range(2, len(decrypted_bytes)):
            if decrypted_bytes[i] == 0x00:
                separator_index = i
                break

        if separator_index == -1:
            raise Exception("找不到PKCS1填充分隔符")

        # 提取实际数据
        actual_data = decrypted_bytes[separator_index + 1:]

        # 返回解密后的字符串
        return actual_data.decode('utf-8')

    except Exception as e:
        raise Exception(f"解密Druid密码失败: {str(e)}")

# 数据库配置相关的环境变量
required_env_vars = ["MYSQL_HOST", "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DB"]

def check_required_env_vars():
    """检查必要的环境变量是否存在"""
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        error_msg = f"缺少必要的环境变量: {', '.join(missing_vars)}"
        return False, error_msg
    return True, None

def get_mysql_config():
    """获取MySQL连接配置"""
    password = os.getenv("MYSQL_PASSWORD")

    # 检查是否启用了Druid密码解密
    use_druid_decrypt = os.getenv("MYSQL_PASSWORD_ENCRYPTED", "false").lower() == "true"

    if use_druid_decrypt and password:
        try:
            # 尝试解密密码
            password = decrypt_druid_password(password)
        except Exception as e:
            # 如果解密失败，记录错误并使用原密码
            print(f"警告: Druid密码解密失败，使用原密码: {e}")

    return {
        "host": os.getenv("MYSQL_HOST"),
        "port": int(os.getenv("MYSQL_PORT", "3306")),
        "user": os.getenv("MYSQL_USER"),
        "password": password,
        "db": os.getenv("MYSQL_DB"),
        "pool_size": int(os.getenv("MYSQL_POOL_SIZE", "5")),
        "max_pool_size": int(os.getenv("MYSQL_MAX_POOL_SIZE", "10")),
        "autocommit": os.getenv("MYSQL_AUTOCOMMIT", "True").lower() == "true",
        "charset": os.getenv("MYSQL_CHARSET", "utf8mb4"),
        "loop": None,
        "pool_recycle": int(os.getenv("MYSQL_POOL_RECYCLE", "3600"))
    }

def get_redis_config():
    """获取Redis连接配置"""
    password = os.getenv("REDIS_PASSWORD")
    
    # 检查是否配置了Sentinel模式
    sentinel_master = os.getenv("REDIS_SENTINEL_MASTER")
    sentinel_nodes_str = os.getenv("REDIS_SENTINEL_NODES")
    
    # 如果配置了Sentinel模式
    if sentinel_master and sentinel_nodes_str:
        # 解析sentinel节点列表，格式为host1:port1,host2:port2,...
        sentinel_nodes = [node.strip() for node in sentinel_nodes_str.split(",")]
        
        return {
            "sentinel_nodes": sentinel_nodes,
            "sentinel_master": sentinel_master,
            "db": int(os.getenv("REDIS_DB", "0")),
            "password": password
        }
    else:
        # 单机模式
        return {
            "host": os.getenv("REDIS_HOST", "localhost"),
            "port": int(os.getenv("REDIS_PORT", "6379")),
            "db": int(os.getenv("REDIS_DB", "0")),
            "password": password
        }

def get_heartbeat_interval():
    """获取数据库心跳间隔"""
    return int(os.getenv("MYSQL_HEARTBEAT_INTERVAL", "60"))  # 默认60秒 
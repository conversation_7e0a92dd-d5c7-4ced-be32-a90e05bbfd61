#!/usr/bin/env python3
"""
测试properties文件加载功能
"""
import os
import sys
from config.app_config import load_config, get_mysql_config, get_redis_config

def test_properties_loading():
    """测试properties文件加载"""
    print("=== 测试Properties文件加载 ===")
    
    # 加载配置
    load_config()
    
    # 测试一些关键配置
    print(f"MYSQL_HOST: {os.getenv('MYSQL_HOST')}")
    print(f"MYSQL_USER: {os.getenv('MYSQL_USER')}")
    print(f"MYSQL_DB: {os.getenv('MYSQL_DB')}")
    print(f"REDIS_HOST: {os.getenv('REDIS_HOST')}")
    print(f"AGENT_MODEL_NAME: {os.getenv('AGENT_MODEL_NAME')}")
    print(f"HOST: {os.getenv('HOST')}")
    print(f"PORT: {os.getenv('PORT')}")
    
    print("\n=== 测试MySQL配置获取 ===")
    try:
        mysql_config = get_mysql_config()
        print("MySQL配置:")
        for key, value in mysql_config.items():
            if key == 'password':
                print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
    except Exception as e:
        print(f"获取MySQL配置失败: {e}")
    
    print("\n=== 测试Redis配置获取 ===")
    try:
        redis_config = get_redis_config()
        print("Redis配置:")
        for key, value in redis_config.items():
            if key == 'password':
                print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
    except Exception as e:
        print(f"获取Redis配置失败: {e}")

def test_properties_loader_directly():
    """直接测试PropertiesLoader类"""
    print("\n=== 直接测试PropertiesLoader ===")
    
    from utils.properties_loader import PropertiesLoader
    
    try:
        loader = PropertiesLoader("app.properties")
        properties = loader.load()
        
        print(f"加载了 {len(properties)} 个配置项")
        print("前10个配置项:")
        for i, (key, value) in enumerate(properties.items()):
            if i >= 10:
                break
            if 'password' in key.lower() or 'key' in key.lower():
                print(f"  {key}: {'*' * len(value) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
        
        # 测试类型转换方法
        print(f"\n类型转换测试:")
        print(f"MYSQL_PORT (int): {loader.get_int('MYSQL_PORT')}")
        print(f"MYSQL_AUTOCOMMIT (bool): {loader.get_bool('MYSQL_AUTOCOMMIT')}")
        print(f"LANGSMITH_TRACING (bool): {loader.get_bool('LANGSMITH_TRACING')}")
        
    except Exception as e:
        print(f"直接测试PropertiesLoader失败: {e}")

if __name__ == "__main__":
    test_properties_loading()
    test_properties_loader_directly()

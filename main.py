"""
应用程序入口点
启动FastAPI应用服务器
"""
import uvicorn
import os
from config.app_config import load_config

if __name__ == "__main__":

    # 加载配置（优先使用properties文件，否则使用环境变量）
    load_config()
    
    # 获取服务器配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = True
    
    # 启动服务器
    uvicorn.run(
        "api.app:app", 
        host=host, 
        port=port, 
        reload=reload,
        log_level="info"
    )
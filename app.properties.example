# 网维小智应用配置文件模板
# 复制此文件为 app.properties 并填入实际配置值

# ===========================================
# API密钥配置
# ===========================================

# 阿里云百炼API密钥
DASHSCOPE_API_KEY=sk-your-dashscope-api-key

# OpenAI兼容API配置
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1

# DeepSeek API配置
# 可以使用官网API或第三方代理
DEEPSEEK_API_KEY=sk-your-deepseek-api-key
DEEPSEEK_API_BASE=https://api.siliconflow.cn/v1

# ===========================================
# LangSmith配置（可选，用于调试和监控）
# ===========================================
LANGSMITH_API_KEY=lsv2_pt_your-langsmith-key
LANGCHAIN_PROJECT=your-project-name
LANGSMITH_TRACING=false

# ===========================================
# 数据库配置
# ===========================================

# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DB=your-database-name

# 连接池配置
MYSQL_POOL_SIZE=5
MYSQL_MAX_POOL_SIZE=10
MYSQL_AUTOCOMMIT=true
MYSQL_CHARSET=utf8mb4

# 数据库维护配置
MYSQL_HEARTBEAT_INTERVAL=60
MYSQL_POOL_RECYCLE=3600

# 是否启用Druid密码解密（可选）
MYSQL_PASSWORD_ENCRYPTED=false

# ===========================================
# Redis配置
# ===========================================

# Redis单机模式配置
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=your-redis-password

# Redis Sentinel模式配置（推荐生产环境）
REDIS_SENTINEL_MASTER=your-sentinel-master-name
REDIS_SENTINEL_NODES=host1:port1,host2:port2,host3:port3
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# ===========================================
# AI模型配置
# ===========================================

# 主要对话模型
# 百炼模型
AGENT_MODEL_NAME=qwen-turbo-2025-04-28
# 或使用DeepSeek模型
# AGENT_MODEL_NAME=deepseek-ai/DeepSeek-V3
# 或DeepSeek官网
# AGENT_MODEL_NAME=deepseek-chat

# 意图识别模型
INTENT_MODEL_NAME=deepseek-ai/DeepSeek-V3

# 标题生成模型
TITLE_MODEL_NAME=qwen3-1.7b

# ===========================================
# 外部API配置
# ===========================================

# 网维系统API配置
WANGWEI_API_BASE_URL=http://your-wangwei-api-host:port
WANGWEI_API_MD5_KEY=your-api-md5-key

# ===========================================
# 服务器配置
# ===========================================

# HTTP服务器配置
HOST=0.0.0.0
PORT=8000

# 日志配置
LOG_DIR=logs

# ===========================================
# 高级配置（可选）
# ===========================================

# 自定义properties文件路径（环境变量）
# PROPERTIES_PATH=config/production.properties

# 自定义.env文件路径（向后兼容）
# DOTENV_PATH=.env.production

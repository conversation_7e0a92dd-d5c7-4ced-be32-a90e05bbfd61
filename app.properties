# 网维小智应用配置文件
# 支持Java风格的properties格式

# API密钥配置
DASHSCOPE_API_KEY=sk-xxxx
OPENAI_API_KEY=sk-xxxxx
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1

# DeepSeek配置
# 硅基流动
DEEPSEEK_API_KEY=sk-rszrgovpckppltutjzyvvfwvzyrnmgbbpeafdyygqfntsqrl
DEEPSEEK_API_BASE=https://api.siliconflow.cn/v1

# LangSmith配置
LANGSMITH_API_KEY=lsv2_pt_xxxxx
LANGCHAIN_PROJECT=wangwei
LANGSMITH_TRACING=true

# MySQL数据库配置
MYSQL_USER=defend_develop_sec
MYSQL_PASSWORD=Swdev#445566
MYSQL_HOST=***************
MYSQL_PORT=3306
MYSQL_DB=wangwei_manage
MYSQL_POOL_SIZE=10
MYSQL_MAX_POOL_SIZE=20
MYSQL_AUTOCOMMIT=true
MYSQL_CHARSET=utf8mb4
MYSQL_HEARTBEAT_INTERVAL=60
MYSQL_POOL_RECYCLE=3600

# Redis配置
REDIS_SENTINEL_MASTER=sentinel-**************-6388
REDIS_SENTINEL_NODES=**************:6390,**************:6389,**************:6389
REDIS_DB=10
REDIS_PASSWORD=xddfgvhjk3456

# 模型配置
# 百炼
AGENT_MODEL_NAME=qwen-turbo-2025-04-28
# 硅基流动
# AGENT_MODEL_NAME=deepseek-ai/DeepSeek-V3
INTENT_MODEL_NAME=deepseek-ai/DeepSeek-V3
# DeepSeek官网
# AGENT_MODEL_NAME=deepseek-chat

# 标题模型
TITLE_MODEL_NAME=qwen3-1.7b

# 网维API配置
WANGWEI_API_BASE_URL=http://*************:9001
WANGWEI_API_MD5_KEY=sjakfnskjr

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 日志配置
LOG_DIR=logs

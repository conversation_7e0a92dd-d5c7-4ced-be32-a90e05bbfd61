# Properties文件迁移指南

本指南说明如何从`.env`文件迁移到`.properties`文件来管理应用配置。

## 概述

项目已经支持使用Java风格的`.properties`文件来替代`.env`文件进行配置管理。新的配置系统具有以下特性：

- 支持Java标准的properties文件格式
- 支持注释（# 和 !）
- 支持续行（反斜杠）
- 支持转义字符
- 向后兼容环境变量
- 类型安全的配置读取

## 文件格式对比

### 原来的.env格式
```bash
# 这是注释
MYSQL_HOST=***************
MYSQL_PORT=3306
MYSQL_USER="defend_develop_sec"
MYSQL_PASSWORD="Swdev#445566"
MYSQL_AUTOCOMMIT=True
```

### 新的.properties格式
```properties
# 这是注释
MYSQL_HOST=***************
MYSQL_PORT=3306
MYSQL_USER=defend_develop_sec
MYSQL_PASSWORD=Swdev#445566
MYSQL_AUTOCOMMIT=true
```

## 主要差异

1. **引号处理**: properties文件中的值不需要引号，引号会被自动移除
2. **布尔值**: 推荐使用`true/false`而不是`True/False`
3. **续行支持**: 可以使用反斜杠进行续行
4. **转义字符**: 支持`\n`, `\r`, `\t`, `\\`, `\=`, `\:`等转义

## 迁移步骤

### 1. 创建app.properties文件

将现有的`.env`文件内容复制到`app.properties`文件中，并根据需要调整格式。

### 2. 更新配置加载

项目已经自动支持properties文件加载，无需修改代码。配置加载优先级：

1. 如果存在`app.properties`文件，优先使用
2. 如果不存在，回退到环境变量
3. 可以通过`PROPERTIES_PATH`环境变量指定自定义properties文件路径

### 3. 测试配置加载

运行测试脚本验证配置加载：

```bash
python test_properties.py
```

## 配置文件位置

### 默认位置
- `app.properties` - 项目根目录

### 自定义位置
通过环境变量指定：
```bash
export PROPERTIES_PATH=/path/to/your/config.properties
```

或在代码中：
```python
os.environ["PROPERTIES_PATH"] = "/path/to/your/config.properties"
```

## Properties文件语法

### 基本语法
```properties
# 注释行
key=value
key:value  # 也支持冒号分隔符
```

### 续行
```properties
long.property=This is a very long line that \
               continues on the next line
```

### 转义字符
```properties
path=C:\\Program Files\\App
message=Hello\nWorld
special.chars=key\=value\:test
```

### 空值
```properties
empty.value=
# 或者
empty.value
```

## 代码使用示例

### 直接使用PropertiesLoader
```python
from utils.properties_loader import PropertiesLoader

# 加载properties文件
loader = PropertiesLoader("app.properties")
loader.load()

# 获取配置值
host = loader.get("MYSQL_HOST", "localhost")
port = loader.get_int("MYSQL_PORT", 3306)
autocommit = loader.get_bool("MYSQL_AUTOCOMMIT", True)

# 设置为环境变量（保持兼容性）
loader.set_environment_variables()
```

### 使用便捷函数
```python
from utils.properties_loader import load_properties_to_env

# 直接加载到环境变量
load_properties_to_env("app.properties")

# 然后可以正常使用os.getenv()
import os
host = os.getenv("MYSQL_HOST")
```

### 在应用中使用
```python
from config.app_config import load_config

# 加载配置（自动选择properties或环境变量）
load_config()

# 使用现有的配置函数
from config.app_config import get_mysql_config, get_redis_config
mysql_config = get_mysql_config()
redis_config = get_redis_config()
```

## 兼容性说明

- 现有代码无需修改，仍然可以使用`os.getenv()`
- 如果同时存在properties文件和环境变量，properties文件优先
- 可以通过设置`PROPERTIES_PATH`为空字符串来禁用properties文件加载

## 最佳实践

1. **敏感信息**: 对于生产环境，建议将敏感配置（如密码、API密钥）通过环境变量传入
2. **文件管理**: 将`app.properties`添加到`.gitignore`，提供`app.properties.example`作为模板
3. **配置分离**: 可以为不同环境创建不同的properties文件
4. **注释**: 在properties文件中添加详细的配置说明注释

## 故障排除

### 配置未生效
1. 检查properties文件路径是否正确
2. 检查文件编码是否为UTF-8
3. 检查语法是否正确（特别是转义字符）

### 类型转换错误
使用PropertiesLoader的类型安全方法：
- `get_int()` - 整数
- `get_bool()` - 布尔值
- `get_float()` - 浮点数

### 调试配置加载
在代码中添加调试输出：
```python
import os
print(f"当前工作目录: {os.getcwd()}")
print(f"Properties文件路径: {os.getenv('PROPERTIES_PATH', 'app.properties')}")
```

"""
命令行聊天工具模块
提供命令行界面与代理交互的功能
"""
import asyncio
import os
import json
from config.app_config import load_config
from api.services.chat_service import generate_chat_responses
from core.db.mysql_pool import AsyncMySQLPool

# 加载配置
load_config()
AsyncMySQLPool.initialize(
        host=os.getenv("MYSQL_HOST"),
        port=int(os.getenv("MYSQL_PORT", "3306")),
        user=os.getenv("MYSQL_USER"),
        password=os.getenv("MYSQL_PASSWORD"),
        db=os.getenv("MYSQL_DB"),
        pool_size=int(os.getenv("MYSQL_POOL_SIZE", "5")),
        max_pool_size=int(os.getenv("MYSQL_MAX_POOL_SIZE", "10")),
        autocommit=os.getenv("MYSQL_AUTOCOMMIT", "True").lower() == "true",
        charset=os.getenv("MYSQL_CHARSET", "utf8mb4"),
        loop=None,
        pool_recycle=3600
    )

# 全局变量存储心跳任务，以便后续能够取消
heartbeat_task = None

async def parse_sse(chunk):
    """解析SSE格式的数据块
    
    Args:
        chunk: SSE格式的数据块
        
    Returns:
        tuple: (event_type, data) 元组，如果无法解析则返回(None, None)
    """
    lines = chunk.strip().split('\n')
    event_type = None
    data = None
    
    for line in lines:
        if line.startswith('event:'):
            event_type = line[6:].strip()
        elif line.startswith('data:'):
            data_str = line[5:].strip()
            try:
                data = json.loads(data_str) if data_str else {}
            except json.JSONDecodeError:
                print(f"\n[解析错误: {data_str}]")
                data = {}
    
    return event_type, data

async def cli_chat():
    """
    命令行聊天工具，用于调试流式输出接口
    """
    global heartbeat_task
    
    try:
        print("欢迎使用网维小智命令行版！输入 'exit' 或 'quit' 退出。")
        
        # 启动数据库连接心跳任务
        heartbeat_interval = int(os.getenv("MYSQL_HEARTBEAT_INTERVAL", "60"))  # 默认60秒
        print(f"启动数据库连接心跳，间隔 {heartbeat_interval} 秒...")
        heartbeat_task = asyncio.create_task(AsyncMySQLPool.keep_alive(interval=heartbeat_interval))
        
        # 询问是否进入调试模式
        debug_mode = input("是否开启调试模式? (y/n): ").lower() == 'y'
        
        # 让用户手动输入用户ID
        while True:
            try:
                user_id_input = input("请输入用户ID(整数): ")
                user_id = int(user_id_input)
                break
            except ValueError:
                print("错误: 用户ID必须是整数，请重新输入")
        
        print(f"当前用户ID: {user_id}")
        
        # 会话ID，首次为None，后续使用服务器返回的ID
        session_id = None
        
        while True:
            # 获取用户输入
            message = input("\n请输入消息: ")
            if message.lower() in ['exit', 'quit']:
                print("再见！")
                break
                
            # 默认不是恢复操作
            resume = 0
            resume_action = None
            
            print("\n回复: ", end="", flush=True)
            
            # 调用流式生成函数
            full_response = ""
            tool_results = []
            tool_artifacts = []
            tool_names = []
            tool_args = []  # 新增：存储工具调用参数
            interrupt_detected = False
            interrupt_question = ""
            interrupt_tool_call = None
            
            async for chunk in generate_chat_responses(message, user_id, 1, session_id, resume, resume_action):
                # 解析SSE格式数据
                event_type, data = await parse_sse(chunk)
                
                if event_type and data is not None:
                    # 处理不同类型的事件
                    if event_type == "checkpoint":
                        session_id = data["session_id"]
                        print(f"session_id: {session_id}")
                    elif event_type == "message":
                        # 直接打印内容，不换行
                        print(data["content"], end="", flush=True)
                        full_response += data["content"]
                    elif event_type == "tool_start":
                        # 处理工具调用开始事件
                        if debug_mode:
                            print(f"\n[调试] 开始调用工具: {data['name']}")
                            print(f"[调试] 参数: {json.dumps(data['args'], ensure_ascii=False)}")
                        tool_names.append(data['name'])
                        tool_args.append(data['args'])
                    elif event_type == "tool_result":
                        tool_result = data["content"]
                        tool_artifact = data["artifact"]
                        tool_name = data["name"]
                        tool_results.append(tool_result)
                        tool_artifacts.append(tool_artifact)
                        tool_names.append(tool_name)
                    elif event_type == "title":
                        print(f"\ntitle: {data["content"]}")
                    elif event_type == "interrupt":
                        # 处理中断事件
                        interrupt_detected = True
                        interrupt_question = data["question"]
                        interrupt_tool_call = data["tool_call"]
                        print(f"\n[中断] {interrupt_question}")
                        print(f"[中断] 工具调用: {interrupt_tool_call['name']}")
                        if debug_mode:
                            print(f"[中断] 详细信息: {json.dumps(interrupt_tool_call, ensure_ascii=False, indent=2)}")
                    elif event_type == "error":
                        print(f"\n[错误] {data['message']}")
                    elif event_type == "end":
                        # 消息结束，打印换行
                        print()
            
            # 如果检测到中断，询问用户如何处理
            if interrupt_detected:
                while True:
                    action = input(f"\n{interrupt_question} (y/n): ").lower()
                    if action in ['y', 'yes']:
                        resume_action = "continue"
                        break
                    elif action in ['n', 'no']:
                        resume_action = "reject"
                        break
                    else:
                        print("请输入 y 或 n")
                
                # 恢复执行
                print("\n继续执行: ", end="", flush=True)
                resume = 1
                
                # 再次调用流式生成函数，这次是恢复操作
                async for chunk in generate_chat_responses("", user_id, 1, session_id, resume, resume_action):
                    # 解析SSE格式数据
                    event_type, data = await parse_sse(chunk)
                    
                    if event_type and data is not None:
                        # 处理不同类型的事件
                        if event_type == "message":
                            # 直接打印内容，不换行
                            print(data["content"], end="", flush=True)
                            full_response += data["content"]
                        elif event_type == "tool_start":
                            # 处理工具调用开始事件
                            if debug_mode:
                                print(f"\n[调试] 开始调用工具: {data['name']}")
                                print(f"[调试] 参数: {json.dumps(data['args'], ensure_ascii=False)}")
                            tool_names.append(data['name'])
                            tool_args.append(data['args'])
                        elif event_type == "tool_result":
                            tool_result = data["content"]
                            tool_artifact = data["artifact"]
                            tool_name = data["name"]
                            tool_results.append(tool_result)
                            tool_artifacts.append(tool_artifact)
                            tool_names.append(tool_name)
                        elif event_type == "title":
                            print(f"\ntitle: {data["content"]}")
                        elif event_type == "error":
                            print(f"\n[错误] {data['message']}")
                        elif event_type == "end":
                            # 消息结束，打印换行
                            print()

            # 在调试模式下，消息完成后显示总结信息
            if debug_mode:
                print("\n[调试] 总结信息:")
                print(f"[调试] 工具调用次数: {len(tool_names)}")
                for i, (name, args) in enumerate(zip(tool_names, tool_args), 1):
                    print(f"[调试] 工具 {i}: {name}")
                    print(f"[调试] 参数: {json.dumps(args, ensure_ascii=False)}")
                    if i <= len(tool_results):
                        print(f"[调试] 结果: {tool_results[i-1]}")
                        if tool_artifacts[i-1]:
                            print(f"[调试] 附加数据: {json.dumps(tool_artifacts[i-1], ensure_ascii=False, indent=2)}")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序出错: {e}")
    finally:
        # 无论如何都要关闭心跳任务和连接池
        if heartbeat_task and not heartbeat_task.done():
            print("正在停止数据库心跳任务...")
            heartbeat_task.cancel()
            try:
                await heartbeat_task
            except asyncio.CancelledError:
                pass
            print("数据库心跳任务已停止")
        
        try:
            await AsyncMySQLPool.close()
            print("数据库连接池已关闭")
        except Exception as e:
            print(f"关闭数据库连接池时出错: {e}")

def main():
    """命令行主入口函数"""
    asyncio.run(cli_chat())

if __name__ == "__main__":
    main() 
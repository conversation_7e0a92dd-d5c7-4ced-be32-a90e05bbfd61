"""
Properties文件加载器
支持加载Java风格的.properties配置文件
"""
import os
import re
from typing import Dict, Optional


class PropertiesLoader:
    """Properties文件加载器类"""
    
    def __init__(self, file_path: str = None):
        """
        初始化Properties加载器
        
        Args:
            file_path (str, optional): properties文件路径，默认为当前目录下的app.properties
        """
        if file_path is None:
            file_path = os.path.join(os.getcwd(), "app.properties")
        self.file_path = file_path
        self.properties = {}
        
    def load(self) -> Dict[str, str]:
        """
        加载properties文件
        
        Returns:
            Dict[str, str]: 配置键值对字典
            
        Raises:
            FileNotFoundError: 当properties文件不存在时
            Exception: 当文件解析失败时
        """
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"Properties文件不存在: {self.file_path}")
            
        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                self.properties = self._parse_properties(file.readlines())
            return self.properties
        except Exception as e:
            raise Exception(f"解析properties文件失败: {str(e)}")
    
    def _parse_properties(self, lines: list) -> Dict[str, str]:
        """
        解析properties文件内容
        
        Args:
            lines (list): 文件行列表
            
        Returns:
            Dict[str, str]: 解析后的配置字典
        """
        properties = {}
        continuation_line = ""
        
        for line in lines:
            line = line.strip()
            
            # 跳过空行和注释行
            if not line or line.startswith('#') or line.startswith('!'):
                continue
                
            # 处理续行（以反斜杠结尾）
            if line.endswith('\\'):
                continuation_line += line[:-1]
                continue
            else:
                line = continuation_line + line
                continuation_line = ""
            
            # 查找键值分隔符（= 或 :）
            separator_match = re.search(r'(?<!\\)[=:]', line)
            if separator_match:
                key = line[:separator_match.start()].strip()
                value = line[separator_match.end():].strip()
                
                # 处理转义字符
                key = self._unescape_string(key)
                value = self._unescape_string(value)
                
                # 移除值两端的引号（如果存在）
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                properties[key] = value
        
        return properties
    
    def _unescape_string(self, s: str) -> str:
        """
        处理字符串中的转义字符
        
        Args:
            s (str): 原始字符串
            
        Returns:
            str: 处理转义字符后的字符串
        """
        # 处理常见的转义字符
        s = s.replace('\\n', '\n')
        s = s.replace('\\r', '\r')
        s = s.replace('\\t', '\t')
        s = s.replace('\\\\', '\\')
        s = s.replace('\\=', '=')
        s = s.replace('\\:', ':')
        s = s.replace('\\ ', ' ')
        return s
    
    def get(self, key: str, default: str = None) -> Optional[str]:
        """
        获取配置值
        
        Args:
            key (str): 配置键
            default (str, optional): 默认值
            
        Returns:
            Optional[str]: 配置值或默认值
        """
        return self.properties.get(key, default)
    
    def get_int(self, key: str, default: int = None) -> Optional[int]:
        """
        获取整数类型的配置值
        
        Args:
            key (str): 配置键
            default (int, optional): 默认值
            
        Returns:
            Optional[int]: 整数配置值或默认值
        """
        value = self.get(key)
        if value is None:
            return default
        try:
            return int(value)
        except ValueError:
            return default
    
    def get_bool(self, key: str, default: bool = None) -> Optional[bool]:
        """
        获取布尔类型的配置值
        
        Args:
            key (str): 配置键
            default (bool, optional): 默认值
            
        Returns:
            Optional[bool]: 布尔配置值或默认值
        """
        value = self.get(key)
        if value is None:
            return default
        return value.lower() in ('true', 'yes', '1', 'on')
    
    def get_float(self, key: str, default: float = None) -> Optional[float]:
        """
        获取浮点数类型的配置值
        
        Args:
            key (str): 配置键
            default (float, optional): 默认值
            
        Returns:
            Optional[float]: 浮点数配置值或默认值
        """
        value = self.get(key)
        if value is None:
            return default
        try:
            return float(value)
        except ValueError:
            return default
    
    def set_environment_variables(self):
        """
        将properties中的配置设置为环境变量
        这样可以保持与现有代码的兼容性
        """
        for key, value in self.properties.items():
            os.environ[key] = value
    
    def __contains__(self, key: str) -> bool:
        """
        检查是否包含指定的配置键
        
        Args:
            key (str): 配置键
            
        Returns:
            bool: 是否包含该键
        """
        return key in self.properties
    
    def __getitem__(self, key: str) -> str:
        """
        通过索引方式获取配置值
        
        Args:
            key (str): 配置键
            
        Returns:
            str: 配置值
            
        Raises:
            KeyError: 当键不存在时
        """
        return self.properties[key]


def load_properties(file_path: str = None) -> PropertiesLoader:
    """
    便捷函数：加载properties文件
    
    Args:
        file_path (str, optional): properties文件路径
        
    Returns:
        PropertiesLoader: 加载器实例
    """
    loader = PropertiesLoader(file_path)
    loader.load()
    return loader


def load_properties_to_env(file_path: str = None):
    """
    便捷函数：加载properties文件并设置为环境变量
    
    Args:
        file_path (str, optional): properties文件路径
    """
    loader = load_properties(file_path)
    loader.set_environment_variables()
